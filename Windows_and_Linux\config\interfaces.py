"""
Writing Tools - Configuration Interfaces
Defines the data structures for unified settings management
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional


@dataclass
class ActionConfig:
    """Configuration for a single writing action/command"""

    prefix: str
    instruction: str
    icon: str
    open_in_window: bool = False


@dataclass
class SystemConfig:
    """System-wide configuration settings"""

    # API Configuration
    api_key: str = ""
    provider: str = "Gemini"
    model: str = "gemini-2.0-flash"

    # UI Configuration
    hotkey: str = "ctrl+space"
    theme: str = "gradient"

    # Application Settings
    language: str = "en"
    auto_update: bool = True
    run_mode: str = "dev"  # dev, build_dev, build_final

    # Provider-specific settings
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "llama3.2"
    ollama_keep_alive: str = "5m"

    mistral_base_url: str = "https://api.mistral.ai/v1"
    mistral_model: str = "mistral-large-latest"

    anthropic_model: str = "claude-3-5-sonnet-20241022"

    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-4o"


@dataclass
class UnifiedSettings:
    """Main settings container that holds all configuration data"""

    system: SystemConfig = field(default_factory=SystemConfig)
    actions: Dict[str, ActionConfig] = field(default_factory=dict)
    custom_data: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """Ensure custom_data is initialized"""
        if self.custom_data is None:
            self.custom_data = {}
